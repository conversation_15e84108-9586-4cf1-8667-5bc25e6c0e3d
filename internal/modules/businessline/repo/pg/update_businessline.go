package pg

import (
	"context"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/pg"
	"github.com/jackc/pgx/v5/pgxpool"
)

func (b *businessLinePostgreRepo) Update(ctx context.Context, businessLine model.BusinessLine) error {
	return pg.ExecuteInSchema(ctx, b.pool, func(ctx context.Context, conn *pgxpool.Conn) error {
		query := `
			UPDATE business_lines
			SET name = $2, code = $3, parent_id = $4, updated_at = CURRENT_TIMESTAMP
			WHERE id = $1 AND deleted_at IS NULL
		`

		result, err := conn.Exec(ctx, query,
			businessLine.ID,
			businessLine.Name,
			businessLine.Code,
			businessLine.ParentID,
		)

		if err != nil {
			return utils.InternalErrorf("failed to update business line", err, nil)
		}

		rowsAffected := result.RowsAffected()
		if rowsAffected == 0 {
			return model.BusinessLineNotFoundf("Business line not found", nil, nil)
		}

		return nil
	})
}
