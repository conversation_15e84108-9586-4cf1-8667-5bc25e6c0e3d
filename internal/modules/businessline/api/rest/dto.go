package rest

import (
	"time"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/model"
)

type businessLineResult struct {
	ID       string     `json:"id"`
	Name     string     `json:"name"`
	Code     string     `json:"code"`
	ParentID *string    `json:"parent_id"`
	CreatedAt *time.Time `json:"created_at"`
	UpdatedAt *time.Time `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at"`
}

func businessLineToResult(businessLine *model.BusinessLine) businessLineResult {
	return businessLineResult{
		ID:       businessLine.ID,
		Name:     businessLine.Name,
		Code:     businessLine.Code,
		ParentID: businessLine.ParentID,
		CreatedAt: businessLine.CreatedAt,
		UpdatedAt: businessLine.UpdatedAt,
		DeletedAt: businessLine.DeletedAt,
	}
}

type businessLineCreate struct {
	Name     string  `json:"name" validate:"required"`
	Code     string  `json:"code" validate:"required"`
	ParentID *string `json:"parent_id"`
}

func businessLineCreateToModel(dto businessLineCreate) model.BusinessLineCreate {
	return model.BusinessLineCreate{
		Name:     dto.Name,
		Code:     dto.Code,
		ParentID: dto.ParentID,
	}
}

type businessLineUpdate struct {
	ID       string  `json:"id" validate:"required"`
	Name     string  `json:"name" validate:"required"`
	Code     string  `json:"code" validate:"required"`
	ParentID *string `json:"parent_id"`
}

func businessLineUpdateToModel(dto businessLineUpdate) model.BusinessLineUpdate {
	return model.BusinessLineUpdate{
		ID:       dto.ID,
		Name:     dto.Name,
		Code:     dto.Code,
		ParentID: dto.ParentID,
	}
}

type businessLineWithDetailsResult struct {
	BusinessLine businessLineResult   `json:"business_line"`
	Sublines     []businessLineResult `json:"sublines"`
	Parent       *businessLineResult  `json:"parent"`
}

func businessLineWithDetailsToResult(details *model.BusinessLineWithSublines) businessLineWithDetailsResult {
	result := businessLineWithDetailsResult{
		BusinessLine: businessLineToResult(&details.BusinessLine),
		Sublines:     make([]businessLineResult, len(details.Sublines)),
	}

	for i, subline := range details.Sublines {
		result.Sublines[i] = businessLineToResult(&subline)
	}

	if details.Parent != nil {
		parentResult := businessLineToResult(details.Parent)
		result.Parent = &parentResult
	}

	return result
}
