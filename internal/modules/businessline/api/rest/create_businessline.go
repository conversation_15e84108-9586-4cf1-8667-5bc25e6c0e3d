package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Create implements BusinessLineHandler.
func (b *businessLineHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[businessLineCreate](w, r, b.validator)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		return
	}

	id, err := b.useCase.Create(ctx, businessLineCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Failed to create business line")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}
