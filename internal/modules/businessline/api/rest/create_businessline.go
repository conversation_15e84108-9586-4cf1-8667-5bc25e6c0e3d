package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type businessLineCreate struct {
	Name     string  `json:"name" validate:"required"`
	Code     string  `json:"code" validate:"required"`
	ParentID *string `json:"parent_id"`
}

func businessLineCreateToModel(dto businessLineCreate) model.BusinessLineCreate {
	return model.BusinessLineCreate{
		Name:     dto.Name,
		Code:     dto.Code,
		ParentID: dto.ParentID,
	}
}

// Create implements BusinessLineHandler.
func (b *businessLineHandler) Create(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[businessLineCreate](w, r, b.validator)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		return
	}

	id, err := b.useCase.Create(ctx, businessLineCreateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Failed to create business line")
		return
	}

	rest.SuccessDResponse(w, r, id, http.StatusCreated)
}
