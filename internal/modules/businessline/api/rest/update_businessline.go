package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/modules/businessline/model"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

type businessLineUpdate struct {
	ID       string  `json:"id" validate:"required"`
	Name     string  `json:"name" validate:"required"`
	Code     string  `json:"code" validate:"required"`
	ParentID *string `json:"parent_id"`
}

func businessLineUpdateToModel(dto businessLineUpdate) model.BusinessLineUpdate {
	return model.BusinessLineUpdate{
		ID:       dto.ID,
		Name:     dto.Name,
		Code:     dto.Code,
		ParentID: dto.ParentID,
	}
}

// Update implements BusinessLineHandler.
func (b *businessLineHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[businessLineUpdate](w, r, b.validator)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		return
	}

	err = b.useCase.Update(ctx, businessLineUpdateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Failed to update business line")
		return
	}

	rest.SuccessDResponse(w, r, "Business line updated successfully", http.StatusOK)
}
