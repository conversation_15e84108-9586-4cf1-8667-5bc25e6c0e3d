package rest

import (
	"net/http"

	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils"
	"github.com/JosueDiazC/fhyona-v2-backend/internal/utils/rest"
)

// Update implements BusinessLineHandler.
func (b *businessLineHandler) Update(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	req, err := rest.DecodeAndValidate[businessLineUpdate](w, r, b.validator)
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		return
	}

	err = b.useCase.Update(ctx, businessLineUpdateToModel(*req))
	if err != nil {
		utils.LogErr(ctx, b.log, err)
		respErrHandler(w, r, err, "Failed to update business line")
		return
	}

	rest.SuccessDResponse(w, r, "Business line updated successfully", http.StatusOK)
}
