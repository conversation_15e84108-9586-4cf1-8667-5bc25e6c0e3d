package model

import "time"

type BusinessLine struct {
	ID           string
	Name         string
	Code         string
	ParentID     *string // Self-referencing foreign key (nullable)
	CreatedAt    *time.Time
	UpdatedAt    *time.Time
	DeletedAt    *time.Time
}

type BusinessLineCreate struct {
	Name     string
	Code     string
	ParentID *string
}

type BusinessLineUpdate struct {
	ID       string
	Name     string
	Code     string
	ParentID *string
}

type BusinessLineWithSublines struct {
	BusinessLine BusinessLine
	Sublines     []BusinessLine
	Parent       *BusinessLine
}
